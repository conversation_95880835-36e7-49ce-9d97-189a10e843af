import { PortfolioItemStatus } from '@common/enums';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsUUID,
} from 'class-validator';

export class PortfolioItemEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioId: string;

  @IsEnum(PortfolioItemStatus)
  currentStatus: PortfolioItemStatus = PortfolioItemStatus.PENDING;

  @IsString()
  @IsNotEmpty()
  @IsPhoneNumber('BR')
  readonly phoneNumber: string;

  @IsString()
  @IsOptional()
  readonly name?: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customDataId: string;

  @IsUUID('4')
  readonly middlewareResponseOutputId?: string;

  @IsNumber({ allowNaN: false, allowInfinity: false, maxDecimalPlaces: 0 })
  readonly line: number;

  @IsDate()
  @IsOptional()
  readonly lastInteraction?: Date;

  @IsDate()
  @IsOptional()
  readonly lastMessageSentAt?: Date;

  @IsDate()
  @IsOptional()
  readonly firstMessageSentAt?: Date;

  @IsDate()
  @IsOptional()
  readonly lastFollowUpAt?: Date;

  @IsBoolean()
  @IsOptional()
  waitingBusinessUserResponse: boolean = false;

  @IsNumber({ allowNaN: false, allowInfinity: false, maxDecimalPlaces: 0 })
  @IsNotEmpty()
  readonly followUpCount: number;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(
    id: string,
    portfolioId: string,
    phoneNumber: string,
    customDataId: string,
    line: number,
    name?: string,
    middlewareResponseOutputId?: string,
    lastInteraction?: Date,
    lastMessageSentAt?: Date,
    firstMessageSentAt?: Date,
    lastFollowUpAt?: Date,
    currentStatus?: PortfolioItemStatus | null,
    followUpCount?: number,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.portfolioId = portfolioId;
    this.currentStatus = currentStatus;
    this.phoneNumber = phoneNumber;
    this.name = name;
    this.customDataId = customDataId;
    this.line = line;
    this.middlewareResponseOutputId = middlewareResponseOutputId;
    this.lastInteraction = lastInteraction;
    this.lastMessageSentAt = lastMessageSentAt;
    this.firstMessageSentAt = firstMessageSentAt;
    this.lastFollowUpAt = lastFollowUpAt;
    this.followUpCount = followUpCount;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
