import { SendMessageRequestDto } from '@business-base/infrastructure/adapters/http/dto/send-message-request.dto';
import { CustomerPhoneDto } from '@business-base/infrastructure/dto/out/customer-phone.dto';
import { CommunicationChannel } from '@common/enums';
import { PhoneVerificationDto } from '@business-base/infrastructure/adapters/http/dto/phone-verification.dto';

export interface InfraMessageHubPort {
  createCustomerPhone(customerId: string, customerPhoneDto: CustomerPhoneDto): Promise<void>;

  getCustomerPhonesByCustomerId(customerId: string): Promise<CustomerPhoneDto[]>;

  getCustomerPhoneByCustomerIdAndPhoneNumber(
    customerId: string,
    phoneNumber: string,
  ): Promise<CustomerPhoneDto>;

  getCustomerPhoneByPhoneNumberAndCommunicationChannel(
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneDto>;

  getCustomerPhoneByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneDto>;

  deleteCustomerPhones(customerId: string): Promise<void>;

  deleteCustomerPhone(customerId: string, phoneNumber: string): Promise<void>;

  deleteCustomerPhoneByCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void>;

  sendMessage(message: SendMessageRequestDto): Promise<void>;

  createPhoneVerification(phoneVerification: PhoneVerificationDto): Promise<void>;
}
