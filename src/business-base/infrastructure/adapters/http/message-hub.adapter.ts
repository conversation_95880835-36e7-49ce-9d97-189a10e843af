import { InfraMessageHubPort } from '@business-base/infrastructure/ports/http/message-hub.port';
import { logger } from '@edutalent/commons-sdk';
import { SendMessageRequestDto } from '@business-base/infrastructure/adapters/http/dto/send-message-request.dto';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { CustomerPhoneDto } from '@business-base/infrastructure/dto/out/customer-phone.dto';
import { CommunicationChannel } from '@common/enums';
import { PhoneVerificationDto } from '@business-base/infrastructure/adapters/http/dto/phone-verification.dto';

@Injectable()
export class InfraMessageHubAdapter implements InfraMessageHubPort {
  private readonly messageHubServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.messageHubServiceUrl = String(process.env.MESSAGEHUB_SERVICE_URL);
  }

  async createCustomerPhone(customerId: string, customerPhoneDto: CustomerPhoneDto): Promise<void> {
    logger.debug(
      `Creating customer: ${customerId}, phone in message hub... ${JSON.stringify(
        customerPhoneDto,
      )}`,
    );

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/customers/${customerId}/phones`;
      logger.debug(`Posting data to: ${url}`);

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      await lastValueFrom(this.httpService.post(url, customerPhoneDto, { headers }));
    } catch (error) {
      handleHttpError(error, 'Infra-MessageHub-adapter:createCustomerPhone');
    }
  }

  async getCustomerPhonesByCustomerId(customerId: string): Promise<CustomerPhoneDto[]> {
    logger.debug(`Retrieving all customer:${customerId} phones in message hub...`);

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/customers/${customerId}/phones`;
      logger.debug(`Getting data from: ${url}`);

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const response = await lastValueFrom(this.httpService.get(url, { headers }));
      return response.data?.data;
    } catch (error) {
      handleHttpError(error, 'Infra-MessageHub-adapter:getCustomerPhonesByCustomerId');
    }
  }

  async getCustomerPhoneByPhoneNumberAndCommunicationChannel(
    phoneNumber: string,
    communicationChanel: CommunicationChannel,
  ): Promise<CustomerPhoneDto> {
    logger.debug(
      `Retrieving phone number: ${phoneNumber}, by communication channel: ${communicationChanel} in message hub...`,
    );

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/phones/${phoneNumber}/communication-channel/${communicationChanel}`;
      logger.debug(`Getting data from: ${url}`);

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const response = await lastValueFrom(this.httpService.get(url, { headers }));
      return response.data?.data;
    } catch (error) {
      handleHttpError(
        error,
        'Infra-MessageHub-adapter:getCustomerPhonesByPhoneNumberAndCommunicationChannel',
      );
    }
  }

  async getCustomerPhoneByCustomerIdAndPhoneNumber(
    customerId: string,
    phoneNumber: string,
  ): Promise<CustomerPhoneDto> {
    logger.debug(`Retrieving customer:${customerId} phone:${phoneNumber} in message hub...`);

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/customers/${customerId}/phones/${phoneNumber}`;
      logger.debug(`Getting data from: ${url}`);

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const response = await lastValueFrom(this.httpService.get(url, { headers }));
      return response.data?.data;
    } catch (error) {
      handleHttpError(error, 'Infra-MessageHub-adapter:getCustomerPhoneByCustomerIdAndPhoneNumber');
    }
  }

  async getCustomerPhoneByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneDto> {
    logger.debug(
      `Retrieving customer:${customerId} phone:${phoneNumber} and communication channel: ${communicationChannel} in message hub...`,
    );

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/customers/${customerId}/phones/${phoneNumber}/communication-channels/${communicationChannel}`;
      logger.debug(`Getting data from: ${url}`);

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const response = await lastValueFrom(this.httpService.get(url, { headers }));
      return response.data?.data;
    } catch (error) {
      handleHttpError(
        error,
        'Infra-MessageHub-adapter:getCustomerPhoneByCustomerIdAndPhoneNumberAndCommunicationChannel',
      );
    }
  }

  async deleteCustomerPhones(customerId: string): Promise<void> {
    logger.debug(`Deleting all customer:${customerId} phones in message hub...`);

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/customers/${customerId}/phones`;
      logger.debug(`Deleting data in: ${url}`);

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      await lastValueFrom(this.httpService.delete(url, { headers }));
    } catch (error) {
      handleHttpError(error, 'Infra-MessageHub-adapter:deleteCustomerPhones');
    }
  }

  async deleteCustomerPhone(customerId: string, phoneNumber: string): Promise<void> {
    logger.debug(`Deleting customer:${customerId} phone:${phoneNumber} in message hub...`);

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/customers/${customerId}/phones/${phoneNumber}`;
      logger.debug(`Deleting data in: ${url}`);

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      await lastValueFrom(this.httpService.get(url, { headers }));
    } catch (error) {
      handleHttpError(error, 'Infra-MessageHub-adapter:deleteCustomerPhone');
    }
  }

  async deleteCustomerPhoneByCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void> {
    logger.debug(
      `Deleting customer:${customerId} phone:${phoneNumber} for communication channel: ${communicationChannel} in message hub...`,
    );

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/customers/${customerId}/phones/${phoneNumber}/communication-channels/${communicationChannel}`;
      logger.debug(`Deleting data in: ${url}`);

      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      await lastValueFrom(this.httpService.get(url, { headers }));
    } catch (error) {
      handleHttpError(error, 'Infra-MessageHub-adapter:deleteCustomerPhoneByCommunicationChannel');
    }
  }

  async sendMessage(message: SendMessageRequestDto): Promise<void> {
    logger.debug(`Sending message to message hub: ${message}`);

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/message`;
      logger.debug(`Posting data to: ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      await lastValueFrom(this.httpService.post(url, message, { headers }));
    } catch (error) {
      handleHttpError(error, 'Infra-MessageHub-adapter:sendMessage');
    }
  }

  async createPhoneVerification(phoneVerification: PhoneVerificationDto): Promise<void> {
    logger.info(`Creating phone verification in message hub: ${JSON.stringify(phoneVerification)}`);

    try {
      const url = `${this.messageHubServiceUrl}/api/v1/message-hub/verify/phones`;
      logger.info(`Posting data to: ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      await lastValueFrom(this.httpService.post(url, phoneVerification, { headers }));
    } catch (error) {
      handleHttpError(error, 'Infra-MessageHub-adapter:createPhoneVerification');
    }
  }
}
