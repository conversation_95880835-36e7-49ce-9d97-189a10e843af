import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { InfraBusinessBasePort } from '@message-hub/infrastructure/ports/http/business-base.port';
import { CommunicationChannel, MessageType } from '@common/enums';
import { ResponsePortfolioItemDto } from '@message-hub/infrastructure/dto/response-portfolio-item.dto';

@Injectable()
export class InfraBusinessBaseAdapter implements InfraBusinessBasePort {
  private readonly businessBaseServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.businessBaseServiceUrl = process.env.BUSINESS_BASE_SERVICE_URL.toString();
  }

  async executeItem(
    from: string,
    to: string,
    message: string,
    messageTypes: MessageType[],
    channel: CommunicationChannel,
    filesUrl: string[],
  ): Promise<void> {
    const executePortfolioItemDto = {
      from,
      to,
      message,
      messageTypes,
      channel,
      filesUrl,
    };

    logger.info(`Executing item in business base: ${JSON.stringify(executePortfolioItemDto)}`);

    try {
      const url = `${this.businessBaseServiceUrl}/api/v1/business-base/portfolio-items/execute`;
      logger.info(`Posting data to: ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      await lastValueFrom(
        this.httpService.post(url, executePortfolioItemDto, { headers, timeout: 29000 }),
      );
    } catch (error) {
      logger.error(
        `Error executing item in business base: ${JSON.stringify(
          executePortfolioItemDto,
        )}Error: ${JSON.stringify(error)}`,
      );
      handleHttpError(error, 'Infra-BusinessBase-adapter');
    }
  }

  async updateFirstMessageSentAt(
    customerId: string,
    phoneNumber: string,
    firstMessageSentAt: Date,
  ): Promise<void> {
    try {
      const url = `${this.businessBaseServiceUrl}/api/v1/business-base/portfolio-items/update-first-message-sent-at/${customerId}/${phoneNumber}`;
      logger.info(`Updating first message sent at: ${url}`);
      await lastValueFrom(this.httpService.put(url, { firstMessageSentAt }, { timeout: 29000 }));
    } catch (error) {
      logger.error(
        `UpdateFirstMessageSentAt: error updating first message sent at failed for customerId: ${customerId} and phoneNumber: ${phoneNumber}. Error: ${JSON.stringify(
          error,
        )}`,
      );
    }
  }

  async updateItemStatusNumberNotExist(phoneNumber: string): Promise<ResponsePortfolioItemDto[]> {
    logger.info(
      `Updating all items with phone number: ${phoneNumber} in business base to status NUMBER_NOT_EXIST`,
    );

    try {
      const url = `${this.businessBaseServiceUrl}/api/v1/business-base/portfolio-items/phoneNumber/${phoneNumber}/not-exists`;
      logger.info(`Posting data to: ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const response = await lastValueFrom(
        this.httpService.put(url, {}, { headers, timeout: 29000 }),
      );
      return response.data;
    } catch (error) {
      logger.error(
        `Error update item in business base to status NUMBER_NOT_EXISTS: ${phoneNumber}. Error: ${JSON.stringify(
          error,
        )}`,
      );
      handleHttpError(error, 'Infra-BusinessBase-adapter');
    }
  }
}
