import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { CustomerPhoneDestinationEntity } from '@message-hub/domain/entities/customer-phone-destination.entity';
import { CustomerPhoneDestinationPort } from '@message-hub/infrastructure/ports/db/customer-phone-destination.port';
import { CommunicationChannel, RecordStatus } from '@common/enums';
import { Prisma } from '@prisma/client';

@Injectable()
export class CustomerPhoneDestinationAdapter
  extends PrismaCommonAdapter<CustomerPhoneDestinationEntity>
  implements CustomerPhoneDestinationPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'customerPhoneDestination');
  }

  async getByCustomerIdAndDestination(
    customerId: string,
    destination: string,
  ): Promise<CustomerPhoneDestinationEntity | null> {
    const customerPhoneDestination = await this.prisma.client.customerPhoneDestination.findFirst({
      where: {
        customerId,
        destination,
      },
    });

    if (!customerPhoneDestination) {
      return null;
    }

    return {
      ...customerPhoneDestination,
      communicationChannel: customerPhoneDestination.communicationChannel as CommunicationChannel,
      status: customerPhoneDestination.status as RecordStatus,
    };
  }

  async getByCustomerIdAndDestinationAndCommunicationChannel(
    customerId: string,
    destination: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneDestinationEntity | null> {
    const customerPhoneDestination = await this.prisma.client.customerPhoneDestination.findFirst({
      where: {
        customerId,
        destination,
        communicationChannel,
        status: RecordStatus.ACTIVE,
      },
    });

    if (!customerPhoneDestination) {
      return null;
    }

    return {
      ...customerPhoneDestination,
      communicationChannel: customerPhoneDestination.communicationChannel as CommunicationChannel,
      status: customerPhoneDestination.status as RecordStatus,
    };
  }

  async getDeletedByCustomerIdAndPhoneNumberAndDestinationAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    destination: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneDestinationEntity | null> {
    const customerPhoneDestination = await this.prisma.client.customerPhoneDestination.findFirst({
      where: {
        customerId,
        phoneNumber,
        destination,
        communicationChannel,
        status: RecordStatus.DELETED,
      },
    });

    if (!customerPhoneDestination) {
      return null;
    }

    return {
      ...customerPhoneDestination,
      communicationChannel: customerPhoneDestination.communicationChannel as CommunicationChannel,
      status: customerPhoneDestination.status as RecordStatus,
    };
  }

  async deleteAllByCustomerId(customerId: string): Promise<void> {
    await this.prismaClient.customerPhoneDestination.updateMany({
      where: { customerId: customerId },
      data: { status: RecordStatus.DELETED },
    });
  }

  async deleteAllByCustomerIdAndPhoneNumber(
    customerId: string,
    phoneNumber: string,
  ): Promise<void> {
    await this.prismaClient.customerPhoneDestination.updateMany({
      where: { customerId: customerId, phoneNumber: phoneNumber },
      data: { status: RecordStatus.DELETED },
    });
  }

  async deleteAllByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void> {
    await this.prismaClient.customerPhoneDestination.updateMany({
      where: {
        customerId,
        phoneNumber,
        communicationChannel,
      },
      data: { status: RecordStatus.DELETED },
    });
  }

  async getNextPhoneByCustomerIdAndCommunicationChannel(
    customerId: string,
    communicationChannel: CommunicationChannel,
  ): Promise<{ phoneNumber: string; weight: number; count: number }[]> {
    const result = await this.prisma.client.$queryRaw<
      { phoneNumber: string; weight: number; count: number }[]
    >(Prisma.sql`
      SELECT cwp.phone_number AS "phoneNumber", cwp.weight, count(*) AS "count"
      FROM message_hub.customer_phone cwp
             LEFT JOIN message_hub.customer_phones_destination cwpd
                       ON (cwp.phone_number = cwpd.phone_number AND cwpd.status = 'ACTIVE'
                         AND cwpd.communication_channel = ${communicationChannel} AND
                           cwpd.created_at::date = CURRENT_DATE)
      WHERE cwp.customer_id = ${customerId}::uuid
        AND cwp.communication_channel = ${communicationChannel}
        AND cwp.status = 'ACTIVE'
      GROUP BY cwp.phone_number, cwp.weight
      ORDER BY count (*) ASC
    `);

    return result.map(customerPhone => {
      return {
        ...customerPhone,
        weight: Number(customerPhone.weight),
        count: Number(customerPhone.count),
      };
    });
  }

  async activate(entity: CustomerPhoneDestinationEntity): Promise<void> {
    await this.prismaClient.customerPhoneDestination.update({
      where: {
        customer_phone_destination_pkey: {
          customerId: entity.customerId,
          phoneNumber: entity.phoneNumber,
          communicationChannel: entity.communicationChannel,
          destination: entity.destination,
          status: entity.status,
        },
      },
      data: {
        customerId: entity.customerId,
        phoneNumber: entity.phoneNumber,
        destination: entity.destination,
        communicationChannel: entity.communicationChannel,
        status: RecordStatus.ACTIVE,
        updatedAt: new Date(),
      },
    });
  }
}
