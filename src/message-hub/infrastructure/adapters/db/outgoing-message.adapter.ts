import { Inject, Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { OutgoingMessageEntity } from '@message-hub/domain/entities/outgoing-message.entity';
import { OutgoingMessagePort } from '@message-hub/infrastructure/ports/db/outgoing-message.port';
import { Prisma } from '@prisma/client';
import { randomUUID } from 'crypto';
import { CommunicationChannel, MessageType, RecordStatus } from '@common/enums';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { logger } from '@edutalent/commons-sdk';
import { CustomerPhoneEntity } from '@message-hub/domain/entities/customer-phone.entity';
import { DefaultOutgoingMessage } from '@message-hub/application/models/default-message.models';
import { InfraBusinessBasePort } from '@message-hub/infrastructure/ports/http/business-base.port';

@Injectable()
export class OutgoingMessageAdapter
  extends PrismaCommonAdapter<OutgoingMessageEntity>
  implements OutgoingMessagePort
{
  constructor(
    private readonly prisma: PrismaService,
    @Inject('InfraBusinessBasePort')
    readonly businessBaseAdapter: InfraBusinessBasePort,
  ) {
    super(prisma, 'outgoingMessage');
  }

  async insertOutgoingMessage(
    {
      customerId,
      from,
      to,
      messageType,
      message,
      channel,
      status,
      isFirstMessage,
      apiUrl,
      fileUrl,
    }: {
      customerId: string;
      from: string;
      to: string;
      messageType: string;
      message: string;
      channel: string;
      status: string;
      isFirstMessage: boolean;
      apiUrl: string;
      fileUrl: string;
    },
    randomDelay: number,
  ): Promise<void> {
    try {
      return this.prisma.client.$transaction(async tx => {
        // Get customer phone to check daily limit
        const customerPhone = await tx.customerPhone.findFirst({
          where: { phoneNumber: from, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(`Customer phone not found for number: ${from} and channel: ${channel}`);
        }

        // Get the latest scheduled message time
        const result = (await tx.$queryRaw(Prisma.sql`
          SELECT "time_to_go" as "maxTimeToGo"
          FROM "message_hub"."outgoing_message"
          WHERE communication_channel = ${channel}
            AND "from" = ${from}
            AND "status" = 'ACTIVE'
          ORDER BY "time_to_go" DESC LIMIT 1
          FOR UPDATE
        `)) as { maxTimeToGo: Date }[];

        let newTimeToGo: Date;

        const maxTimeToGo = result[0]?.maxTimeToGo ? new Date(result[0].maxTimeToGo) : null;

        const now = new Date(); // current time in UTC

        const lastTimeToGo =
          maxTimeToGo && maxTimeToGo.getTime() > now.getTime() ? maxTimeToGo : now;

        const delayInSeconds = Number(randomDelay);
        const delayInMs = delayInSeconds * 1000;

        if (!isFirstMessage || process.env.NODE_ENV === 'development') {
          newTimeToGo = new Date(now.getTime() + delayInMs);

          logger.info(
            `InsertOutgoingMessage DEBUG   UTC: now: ${now.toISOString()}, maxTimeToGo: ${maxTimeToGo?.toISOString()}, lastTimeToGo: ${lastTimeToGo.toISOString()}, delayInMs: ${delayInMs}, newTimeToGo: ${newTimeToGo.toISOString()}`,
          );
        } else {
          newTimeToGo = new Date(lastTimeToGo.getTime() + delayInMs);

          logger.info(
            `InsertOutgoingMessage DEBUG   UTC: now: ${now.toISOString()}, maxTimeToGo: ${maxTimeToGo?.toISOString()}, lastTimeToGo: ${lastTimeToGo.toISOString()}, delayInMs: ${delayInMs}, newTimeToGo: ${newTimeToGo.toISOString()}`,
          );

          // Function to check if a date is weekend
          const isWeekend = (date: Date): boolean => {
            const day = date.getDay();
            const hours = date.getHours();

            // Saturday (6) is weekend only after 14:00 (2 PM) Brazil time
            if (day === 6 && hours >= 14) {
              return true;
            }

            // Sunday (0) is always weekend
            if (day === 0) {
              return true;
            }

            return false;
          };

          // Function to get next business day
          const getNextBusinessDay = (date: Date): Date => {
            const nextDay = new Date(date);
            nextDay.setDate(nextDay.getDate() + 1);
            if (isWeekend(nextDay)) {
              return getNextBusinessDay(nextDay);
            }
            return nextDay;
          };

          // Function to count scheduled messages for a specific day
          const countScheduledMessagesForDay = async (date: Date): Promise<number> => {
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            const [count] = (await tx.$queryRaw(Prisma.sql`
              SELECT COUNT(*) as count
              FROM "message_hub"."outgoing_message"
              WHERE "from" = ${from}
                AND "communication_channel" = ${channel}
                AND "is_first_message" = true
                AND "status" = 'ACTIVE'
                AND "time_to_go" >= ${startOfDay.toISOString()}::timestamp
                AND "time_to_go" <= ${endOfDay.toISOString()}::timestamp
            `)) as [{ count: number }];

            return count.count;
          };

          // Skip weekends
          if (isWeekend(newTimeToGo)) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
            newTimeToGo.setHours(8, 0, 0, 0);
          }

          // Check if we've reached the daily limit for the current day
          let currentDayCount = await countScheduledMessagesForDay(newTimeToGo);

          // If current day is full, find the next available day
          while (currentDayCount >= customerPhone.dailyLimit) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
            newTimeToGo.setHours(8, 0, 0, 0);
            currentDayCount = await countScheduledMessagesForDay(newTimeToGo);
          }

          // Now that we have a day with available spots, get the latest timeToGo for that day
          const latestMessage = (await tx.$queryRaw(Prisma.sql`
            SELECT "time_to_go" as "maxTimeToGo"
            FROM "message_hub"."outgoing_message"
            WHERE "sent" = false
              AND communication_channel = ${channel}
              AND "from" = ${from}
              AND "status" = 'ACTIVE'
              AND "time_to_go" >= ${newTimeToGo.toISOString()}::timestamp
              AND "time_to_go" < ${new Date(
                newTimeToGo.getTime() + 24 * 60 * 60 * 1000,
              ).toISOString()}::timestamp
            ORDER BY "time_to_go" DESC LIMIT 1
            FOR UPDATE
          `)) as { maxTimeToGo: Date }[];

          // If there are no messages for this day yet, start from the base time
          if (!latestMessage || latestMessage.length === 0) {
            newTimeToGo = new Date(newTimeToGo.getTime() + Number(randomDelay) * 1000);
          } else {
            // If there are messages, add random delay to the latest one
            newTimeToGo = new Date(
              latestMessage[0].maxTimeToGo.getTime() + Number(randomDelay) * 1000,
            );
          }

          // If it's after 20:00, schedule for next business day
          if (newTimeToGo.getHours() >= 20) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
            newTimeToGo.setHours(8, 0, 0, 0);
          }
        }

        await tx.$executeRaw(
          Prisma.sql`
            INSERT INTO "message_hub"."outgoing_message" ("id", "customer_id", "to", "from", "message_type",
                                                          "message",
                                                          "communication_channel", "time_to_go", "sent",
                                                          "status", "created_at", "updated_at",
                                                          "api_url", "file_url", "is_first_message")
            VALUES (${randomUUID()}::uuid, ${customerId}::uuid, ${to}, ${from}, ${messageType}, ${message},
                    ${channel}, ${newTimeToGo.toISOString()}::timestamp, false,
                    ${status}, NOW(), NOW(), ${apiUrl}, ${fileUrl}, ${isFirstMessage})
          `,
        );
      });
    } catch (error) {
      throw new Error(`Error inserting outgoing message: ${error}`);
    }
  }

  async processOutgoingMessage(
    fromNumber: string,
    channel: CommunicationChannel,
    sendMessage: (to: string, message: string, apiUrl: string) => Promise<void>,
    sendMessageWithFile: (
      to: string,
      message: string,
      apiUrl: string,
      fileUrl: string,
      fileType: string,
    ) => Promise<void>,
    sendMessageSMS: (from: string, to: string, text: string, apiUrl: string) => Promise<void>,
    sendMessageWhatsappApi: (
      customerPhoneEntity: CustomerPhoneEntity,
      messageOutgoing: DefaultOutgoingMessage,
    ) => Promise<void>,
  ): Promise<void> {
    return this.prisma.client.$transaction(
      async tx => {
        const messages = (await tx.$queryRaw(Prisma.sql`
          SELECT "id",
                 "from",
                 "to",
                 "message",
                 "time_to_go"            AS "timeToGo",
                 "communication_channel" AS "channel",
                 "api_url"               AS "apiUrl",
                 "message_type"          AS "messageType",
                 "file_url"              AS "fileUrl",
                 "customer_id"           AS "customerId",
                 "is_first_message"      AS "isFirstMessage"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND "time_to_go" <= NOW()
            AND "from" = ${fromNumber}
            AND "communication_channel" = ${channel}
            AND "status" = 'ACTIVE'
          ORDER BY "time_to_go" ASC LIMIT 1
              FOR
          UPDATE SKIP LOCKED
        `)) as {
          id: string;
          from: string;
          to: string;
          message: string;
          timeToGo: Date;
          channel: string;
          apiUrl: string;
          messageType: string;
          fileUrl?: string;
          customerId: string;
          isFirstMessage: boolean;
        }[];

        if (!messages || messages.length === 0) {
          return;
        }

        const customerPhone = await tx.customerPhone.findFirst({
          where: { phoneNumber: fromNumber, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(
            `Customer phone not found for number: ${fromNumber} and channel: ${channel}`,
          );
        }

        for (const message of messages) {
          const startSendingDate = new Date();
          try {
            if (message.channel === CommunicationChannel.WHATSAPPSELFHOSTED) {
              if (message.fileUrl) {
                await sendMessageWithFile(
                  message.to,
                  message.message,
                  message.apiUrl,
                  message.fileUrl,
                  message.messageType,
                );
              } else {
                await sendMessage(message.to, message.message, message.apiUrl);
              }
            } else if (message.channel === CommunicationChannel.SMS_VONAGE) {
              await sendMessageSMS(message.from, message.to, message.message, message.apiUrl);
            } else if (message.channel === CommunicationChannel.WHATSAPP_API) {
              const customerPhoneEntity = {
                ...customerPhone,
                communicationChannel: channel,
                status: RecordStatus.ACTIVE,
                incomingCron: '',
                outgoingCron: '',
                createdAt: new Date(),
                updatedAt: new Date(),
                outgoingMaxDelay: Number(customerPhone.outgoingMaxDelay),
                weight: Number(customerPhone.weight),
              };

              const messageOutgoing: DefaultOutgoingMessage = {
                customerId: customerPhone.customerId,
                communicationChannel: channel,
                messageType: message.messageType as MessageType,
                isFirstMessage: false,
                message: message?.message,
                to: message.to,
                fileUrl: message?.fileUrl,
              };

              await sendMessageWhatsappApi(customerPhoneEntity, messageOutgoing);
            } else {
              throw new BusinessException(
                'Outgoing-Message-Adapter',
                `Channel ${message.channel} not supported for number: ${fromNumber}`,
                BusinessExceptionStatus.INVALID_INPUT,
              );
            }

            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "message_hub"."outgoing_message"
                SET "sent"    = true,
                    "sent_at" = NOW()
                WHERE "id" = ${message.id}::uuid
              `,
            );

            if (message.isFirstMessage) {
              this.businessBaseAdapter.updateFirstMessageSentAt(
                customerPhone.customerId,
                message.to,
                new Date(),
              );
            }

            const finishedSendingDate = new Date();
            logger.info(
              `Message sent successfully from phone: ${customerPhone.phoneNumber} to: ${
                message.to
              }. Took: ${
                finishedSendingDate.getTime() - startSendingDate.getTime()
              }ms. Message: ${JSON.stringify(message)}`,
            );
          } catch (error) {
            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "message_hub"."outgoing_message"
                SET "status"  = 'DELETED',
                    "updated_at" = NOW()
                WHERE "id" = ${message.id}::uuid
              `,
            );

            const finishedSendingDate = new Date();
            logger.error(
              `Failed to send message from phone: ${customerPhone.phoneNumber} to: ${
                message.to
              }. Took: ${
                finishedSendingDate.getTime() - startSendingDate.getTime()
              }ms. Message: ${JSON.stringify(message)}.Error: ${error.message}`,
            );
          }
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }

  async getPendingOutgoingMessage(
    customerId: string,
    channel: CommunicationChannel,
    to: string,
  ): Promise<OutgoingMessageEntity[]> {
    return this.prisma.client.$transaction(
      async tx => {
        const messages = (await tx.$queryRaw(Prisma.sql`
          SELECT "id",
                 "from",
                 "to",
                 "message",
                 "time_to_go"            AS "timeToGo",
                 "communication_channel" AS "channel",
                 "api_url"               AS "apiUrl",
                 "message_type"          AS "messageType",
                 "file_url"              AS "fileUrl"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND "customer_id" = ${Prisma.sql`${customerId}::uuid`}
            AND "to" = ${to}
            AND "communication_channel" = ${channel}
            AND "status" = 'ACTIVE'
          ORDER BY "time_to_go" ASC
            FOR
              UPDATE SKIP LOCKED
        `)) as OutgoingMessageEntity[];

        if (!messages || messages.length === 0) {
          // logger.info(
          //   `No pending messages to send for customer: ${customerId} and channel: ${channel}...`,
          // );
          return [];
        }

        const customerPhone = await tx.customerPhone.findFirst({
          where: { customerId: customerId, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(
            `Customer phone not found for customer: ${customerId} and channel: ${channel}`,
          );
        }

        await tx.$executeRaw(
          Prisma.sql`
            UPDATE message_hub.outgoing_message om
            SET sent       = true,
                sent_at    = NOW(),
                updated_at = NOW()
            WHERE om.id IN (${Prisma.join(
              messages.map(message => Prisma.sql`${message.id}::uuid`),
            )});
          `,
        );

        return messages;
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }

  async getTimestampFromDatabase(): Promise<{ now: Date; nowToString: string }> {
    return this.prisma.client.$transaction(async tx => {
      const [currentDatabaseTime] = (await tx.$queryRaw(Prisma.sql`
            SELECT now() as now
        `)) as [{ now: Date }];
      return { now: currentDatabaseTime.now, nowToString: currentDatabaseTime.now.toString() };
    });
  }

  async getTotalFirstMessagesSentByPortfolio(
    customerId: string,
    portfolioId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{ date: string; count: number; total: number }[]> {
    const results = await this.prisma.client.$queryRaw<{ date: string; count: number }[]>(
      Prisma.sql`
        SELECT TO_CHAR(DATE(om.sent_at), 'YYYY-MM-DD') as date,
               COUNT(*) as count
        FROM message_hub.outgoing_message om
        INNER JOIN business_base.portfolio_item pi ON om.to = pi.phone_number
        WHERE om.customer_id = ${customerId}::uuid
          AND pi.portfolio_id = ${portfolioId}::uuid
          AND om.sent = true
          AND om.is_first_message = true
          AND om.sent_at >= ${dateStart.toISOString()}::timestamp
          AND om.sent_at <= ${dateEnd.toISOString()}::timestamp
          AND pi.status = 'ACTIVE'
        GROUP BY DATE(om.sent_at)
        ORDER BY DATE(om.sent_at) ASC
      `,
    );

    // Calculate total count
    const total = results.reduce((sum, row) => sum + Number(row.count), 0);

    // Return results with total included in each row for consistency
    return results.map(row => ({
      date: row.date,
      count: Number(row.count),
      total,
    }));
  }
}
