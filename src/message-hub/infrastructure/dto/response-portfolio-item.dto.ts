import { PortfolioItemStatus } from '@common/enums';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { PortfolioItemDto } from '@message-hub/infrastructure/dto/portfolio-item.dto';

export class ResponsePortfolioItemDto extends PortfolioItemDto {
  @IsEnum(PortfolioItemStatus)
  @IsOptional()
  readonly currentStatus?: PortfolioItemStatus;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customDataId: string;

  @IsUUID('4')
  readonly middlewareResponseOutputId: string;

  readonly middlewareResponseOutput: any;

  @IsDate()
  @IsOptional()
  readonly lastInteraction?: Date;

  @IsDate()
  @IsOptional()
  readonly lastMessageSentAt?: Date;

  @IsDate()
  @IsOptional()
  readonly lastFollowUpAt?: Date;

  @IsNumber()
  @IsOptional()
  readonly followUpCount: number = 0;

  @IsBoolean()
  @IsOptional()
  readonly waitingBusinessUserResponse: boolean = false;

  constructor(
    id: string,
    portfolioId: string,
    phoneNumber: string,
    customDataId: string,
    line: number,
    customData?: any,
    middlewareResponseOutputId?: string,
    middlewareResponseOutput?: any,
    lastInteraction?: Date,
    lastMessageSentAt?: Date,
    lastFollowUpAt?: Date,
    followUpCount?: number,
    waitingBusinessUserResponse?: boolean,
    currentStatus?: PortfolioItemStatus | null,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    super(portfolioId, phoneNumber, customData, line, createdAt, updatedAt);
    this.id = id;
    this.currentStatus = currentStatus;
    this.lastInteraction = lastInteraction;
    this.lastMessageSentAt = lastMessageSentAt;
    this.lastFollowUpAt = lastFollowUpAt;
    this.followUpCount = followUpCount;
    this.waitingBusinessUserResponse = waitingBusinessUserResponse;
    this.middlewareResponseOutputId = middlewareResponseOutputId;
    this.middlewareResponseOutput = middlewareResponseOutput;
    this.customDataId = customDataId;
    this.updatedAt = updatedAt;
    this.createdAt = createdAt;
  }
}
