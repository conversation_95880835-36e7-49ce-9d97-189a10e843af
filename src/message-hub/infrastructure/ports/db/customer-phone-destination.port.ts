import { DbCommonPort } from '@common/db/ports/common.port';
import { CustomerPhoneDestinationEntity } from '@message-hub/domain/entities/customer-phone-destination.entity';
import { CommunicationChannel } from '@common/enums';

export interface CustomerPhoneDestinationPort extends DbC<PERSON>monPort<CustomerPhoneDestinationEntity> {
  getByCustomerIdAndDestination(
    customerId: string,
    destination: string,
  ): Promise<CustomerPhoneDestinationEntity>;

  getByCustomerIdAndDestinationAndCommunicationChannel(
    customerId: string,
    destination: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneDestinationEntity>;

  getDeletedByCustomerIdAndPhoneNumberAndDestinationAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    destination: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneDestinationEntity | null>;

  getNextPhoneByCustomerIdAndCommunicationChannel(
    customerId: string,
    communicationChannel: CommunicationChannel,
  ): Promise<{ phoneNumber: string; weight: number; count: number }[]>;

  deleteAllByCustomerId(customerId: string): Promise<void>;

  deleteAllByCustomerIdAndPhoneNumber(customerId: string, phoneNumber: string): Promise<void>;

  deleteAllByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void>;

  activate(entity: CustomerPhoneDestinationEntity): Promise<void>;
}
