import { CommunicationChannel, MessageType } from '@common/enums';
import { ResponsePortfolioItemDto } from '@message-hub/infrastructure/dto/response-portfolio-item.dto';

export interface InfraBusinessBasePort {
  executeItem(
    from: string,
    to: string,
    message: string,
    messageTypes: MessageType[],
    channel: CommunicationChannel,
    filesUrl?: string[],
  ): Promise<void>;

  updateFirstMessageSentAt(
    customerId: string,
    phoneNumber: string,
    firstMessageSentAt: Date,
  ): Promise<void>;

  updateItemStatusNumberNotExist(phoneNumber: string): Promise<ResponsePortfolioItemDto[]>;
}
