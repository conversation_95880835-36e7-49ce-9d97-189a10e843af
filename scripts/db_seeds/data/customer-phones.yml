customerPhones:
  - customerId: 4cd6d515-2604-4c2c-adad-435acbef1f5c
    phoneNumber: '5511935033506'
    apiUrl: 'http://localhost:3012'
    communicationChannel: 'WHATSAPPSELFHOSTED'
    incomingCron: '* * * * * *'
    outgoingCron: '* * * * * *'
    outgoingMaxDelay: 0
    weight: 100
    dailyLimit: 100

  # - customerId: 791514bb-ff60-42ea-adec-a803630c6560
  #   phoneNumber: '15556406224'
  #   apiUrl: 'https://graph.facebook.com/v22.0/655488337639312/messages'
  #   communicationChannel: 'WHATSAPP_API'
  #   incomingCron: '* * * * * *'
  #   outgoingCron: '* * * * * *'
  #   outgoingMaxDelay: 0
  #   dailyLimit: 10
  # - customerId: 4cd6d515-2604-4c2c-adad-435acbef1f5c
  #   phoneNumber: '5511953259257'
  #   apiUrl: 'https://rest.nexmo.com/sms/json'
  #   communicationChannel: 'SMS_VONAGE'
  #   incomingCron: '*/2 * * * *'
  #   outgoingMaxDelay: 50
